import { motion, SVGMotionProps } from "framer-motion";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import { useRouter } from "next/router";
import { JSX, RefAttributes, useEffect, useState } from "react";
import brandLogo from "../public/arm-fight-jkl/brand-simple-logo.png";
import { useIsMobile } from "@/hooks";

import styles from "../styles/Navigation.module.css";
import Image from "next/image";
import { club } from "@/data/data";
import { LanguageSelect } from "./LanguageSelect";

const Brand = () => (
  <Link href="/">
    <div className="flex items-center">
      <div className="relative mr-3">
        <motion.div
          animate={{
            rotate: [
              0, 10, 0, 15, 0, 15, 5, 30, 10, 70, 40, 30, 45, 25, 40, 35, 75, 130, -15, 5, -5, 0,
            ],
          }}
          transition={{ duration: 5, ease: "easeInOut", delay: 0.5 }}
          whileTap={{
            rotate: [
              0, 10, 0, 15, 0, 15, 5, 30, 10, 70, 40, 30, 45, 25, 40, 35, 75, 130, -15, 5, -5, 0,
            ],
          }}
        >
          <Image
            loading="lazy"
            height={75}
            title="Arm Fight JKL"
            alt={club.brand}
            src={brandLogo}
            className="transition-all duration-500"
          />
        </motion.div>
      </div>
      <h1 className="text-2xl font-semibold">{club.brand}</h1>
    </div>
  </Link>
);

const Path = (
  props: JSX.IntrinsicAttributes & SVGMotionProps<SVGPathElement> & RefAttributes<SVGPathElement>,
) => (
  <motion.path
    className="stroke-light-text"
    fill="transparent"
    stroke="hsl(0, 0%, 18%)"
    strokeLinecap="round"
    strokeWidth="3"
    {...props}
  />
);

const NavToggleButton = ({
  isOpen,
  label,
  toggleOpen,
}: {
  isOpen: boolean;
  label: string;
  toggleOpen: () => void;
}) => (
  <button
    className="z-50 h-9 rounded-full p-2 pt-[10px] md:hidden"
    onClick={toggleOpen}
    aria-controls="navigation-menu"
    aria-expanded={isOpen}
    aria-label={label}
  >
    <svg width="20" height="20" viewBox="0 0 22 22">
      <Path
        animate={isOpen ? "open" : "closed"}
        initial={false}
        variants={{ closed: { d: "M 2 2.5 L 20 2.5" }, open: { d: "M 4 16.5 L 18 2.5" } }}
      />
      <Path
        animate={isOpen ? "open" : "closed"}
        d="M 2 9.423 L 20 9.423"
        initial={false}
        transition={{ duration: 0.1 }}
        variants={{ closed: { opacity: 1 }, open: { opacity: 0 } }}
      />
      <Path
        animate={isOpen ? "open" : "closed"}
        initial={false}
        variants={{ closed: { d: "M 2 16.346 L 20 16.346" }, open: { d: "M 4 2.5 L 18 16.346" } }}
      />
    </svg>
  </button>
);

const NavLink = ({
  children,
  href,
  isActive,
  onNavigate,
  mobile,
}: {
  children: React.ReactNode;
  href: string;
  isActive: boolean;
  onNavigate?: () => void;
  mobile: boolean;
  link: string;
}) => {
  const mobileClasses = mobile ? "p-3 text-xl" : "";
  const activeClasses = isActive ? "font-semibold underline" : "";

  return (
    <li className={`${mobile ? "" : "inline"}`}>
      <Link
        className={`link-navigation inline-block ${activeClasses} ${mobileClasses}`}
        href={href}
        onClick={mobile ? onNavigate : () => {}}
      >
        {children}
      </Link>
    </li>
  );
};

const links: { [key: string]: string } = {
  home: "/",
  // club: "/club",
  register: "/register",
  contact: "/contact",
};

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isTop, setIsTop] = useState(true);
  const isMobile = useIsMobile();
  const router = useRouter();

  const currentPath = router.asPath;
  const { t } = useTranslation("common");

  const handleOnScroll = () => {
    const top = document.documentElement.scrollTop < 20;
    setIsTop(top);
  };

  useEffect(() => {
    window.addEventListener("scroll", handleOnScroll);

    return () => window.removeEventListener("scroll", handleOnScroll);
  }, []);

  return (
    <>
      <nav
        className={`bg-light fixed top-0 right-0 left-0 z-50 flex h-[64px] w-full items-center justify-between px-3 transition-all duration-500 md:px-6 ${isTop ? "" : "border-light-border shadow"}`}
      >
        <div className="mx-auto flex w-full max-w-5xl flex-1 items-center justify-between">
          <Brand />

          {/* Mobile nav */}
          {isMobile && (
            <>
              <div className="flex items-center justify-between gap-2">
                <LanguageSelect />
                <NavToggleButton
                  isOpen={isOpen}
                  label={t("navigation.label")}
                  toggleOpen={() => setIsOpen((prev) => !prev)}
                />
              </div>
              <div
                className={`bg-light z-10 md:hidden ${styles.mobileNav}${
                  isOpen ? ` ${styles.open}` : ""
                }`}
                id="navigation-menu"
                aria-hidden={!isOpen}
              >
                <ul className="mx-20 mt-20 mb-10 text-center">
                  {Object.keys(links).map((key) => (
                    <NavLink
                      href={links[key]}
                      isActive={currentPath === links[key]}
                      key={key}
                      link={key}
                      mobile={isMobile}
                      onNavigate={() => setIsOpen((prev) => !prev)}
                    >
                      {t(`navigation.${key}`)}
                    </NavLink>
                  ))}
                </ul>
              </div>
            </>
          )}

          {/* Desktop nav */}
          {!isMobile && (
            <>
              <div className="z-10 hidden md:block">
                <div className="flex items-center gap-8">
                  <ul className="flex gap-8 text-center">
                    {Object.keys(links).map(
                      (key) =>
                        key !== "home" && (
                          <NavLink
                            href={links[key]}
                            isActive={currentPath === links[key]}
                            key={key}
                            link={key}
                            mobile={isMobile}
                          >
                            {t(`navigation.${key}`)}
                          </NavLink>
                        ),
                    )}
                  </ul>
                  <LanguageSelect />
                </div>
              </div>
            </>
          )}
        </div>
      </nav>

      {/* Spacing for fixed navbar */}
      <div className="h-[64px]" />
    </>
  );
};

export default Navigation;
