import { IconLoader3 } from "@tabler/icons-react";
import { useTranslation } from "next-i18next";
import { useState } from "react";

import { ContactFormData } from "@/interfaces/contactForm";
import brandLogo from "../public/arm-fight-jkl/brand-simple-logo.png";

import Email from "./common/Email";
import ErrorBanner from "./ErrorBanner";
import { club } from "@/data/data";

import { motion } from "framer-motion";
import Image from "next/image";

const sendContactForm = async (data: ContactFormData, language: string) => {
  const response = await fetch(`/api/contact?language=${language}`, {
    method: "POST",
    body: JSON.stringify(data),
    headers: { "Content-Type": "application/json", Accept: "application/json" },
  });

  if (!response.ok) {
    throw new Error("Send contact form failed");
  }

  return await response.json();
};

interface FormBlockProps {
  children: React.ReactNode;
  id: string;
  label: string;
  required: boolean;
}

const FormBlock = ({ children, id, label = "Label", required = true }: FormBlockProps) => (
  <div className="mb-5">
    <label htmlFor={id} className="mb-2 block font-semibold">
      {label} {required && <span className="text-light-error">&#42;</span>}
    </label>
    {children}
  </div>
);

const initFormState = { email: "", message: "" };

const Brand = () => (
  <div className="flex items-center">
    <div className="relative mr-3">
      <motion.div
        animate={{
          rotate: [
            0, 10, 0, 15, 0, 15, 5, 30, 10, 70, 40, 30, 45, 25, 40, 35, 75, 130, -15, 5, -5, 0,
          ],
        }}
        transition={{ duration: 5, ease: "easeInOut", delay: 0.5 }}
        whileTap={{
          rotate: [
            0, 10, 0, 15, 0, 15, 5, 30, 10, 70, 40, 30, 45, 25, 40, 35, 75, 130, -15, 5, -5, 0,
          ],
        }}
      >
        <Image
          loading="lazy"
          height={100}
          title="Arm Fight JKL"
          alt={"Success"}
          src={brandLogo}
          className="transition-all duration-500"
        />
      </motion.div>
    </div>
  </div>
);

const ContactForm = () => {
  const [formData, setFormData] = useState<ContactFormData>(initFormState);
  const [formErrorState, setFormErrorState] = useState<ContactFormData>(initFormState);
  const [submissionLoading, setSubmissionLoading] = useState(false);
  const [submissionError, setSubmissionError] = useState(false);
  const [submissionSuccess, setSubmissionSuccess] = useState(false);

  const emailBorderStyles = `
    ${formErrorState.email ? "border-light-error" : "border-light-text"}`;
  const messageBorderStyles = `
    ${formErrorState.message ? "border-light-error" : "border-light-text"}`;

  const { t, i18n } = useTranslation("common");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    setFormErrorState((prev) => ({ ...prev, [e.target.name]: "" }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!submissionLoading && !submissionError && !submissionSuccess) {
      const errors = { ...initFormState };

      if (!formData.email || !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
        errors.email = t("contact.email-required");
      }

      if (!formData.message) {
        errors.message = t("contact.message-required");
      }

      if (errors.email || errors.message) {
        setFormErrorState(errors);
        return;
      }

      try {
        setSubmissionLoading(true);
        await sendContactForm(formData, i18n.language);
        setSubmissionLoading(false);

        setFormData(initFormState);
        setSubmissionSuccess(true);
      } catch (error) {
        setSubmissionError(true);
        setSubmissionLoading(false);
      }
    }
  };

  return (
    <form className="mx-auto mt-10 w-full items-center" onSubmit={handleSubmit}>
      {submissionSuccess && (
        <div className="mx-auto mt-10 mb-10 h-[470px] w-full max-w-3xl items-center">
          <div className="mb-12 flex h-full flex-grow flex-col items-center justify-center">
            <p className="font-semibold">{t("contact.success-message")}</p>
            <Brand />
          </div>
        </div>
      )}

      {submissionError && <ErrorBanner onDismiss={() => setSubmissionError(false)} />}

      {!submissionSuccess && (
        <>
          <FormBlock id="email" label={t("contact.email")} required>
            <input
              className={`text-light-text block h-[46px] w-full rounded-sm border bg-transparent p-2 leading-none ${emailBorderStyles}`}
              id="email"
              name="email"
              onChange={handleChange}
              type="text"
              value={formData.email}
            />
            {formErrorState.email && (
              <span className="text-light-error">{formErrorState.email}</span>
            )}
          </FormBlock>
          <FormBlock id="message" label={t("contact.message")} required>
            <textarea
              className={`text-light-text block w-full rounded-sm border bg-transparent p-2 ${messageBorderStyles}`}
              id="message"
              name="message"
              onChange={handleChange}
              rows={6}
              value={formData.message}
            />
            {formErrorState.message && (
              <span className="text-light-error">{formErrorState.message}</span>
            )}
          </FormBlock>
          <button
            className="bg-light-accent text-light hover:bg-light-accent-hover h-[46px] w-full rounded-sm p-3 text-lg leading-none transition-colors"
            type="submit"
          >
            {submissionLoading ? (
              <IconLoader3 className="inline animate-spin" size={26} />
            ) : (
              t("contact.send")
            )}
          </button>
        </>
      )}
      <div className="mt-3 text-center">
        <p>
          <span className="font-semibold">{t("contact.via-email")} </span>
          <Email email={club.email} />
        </p>
      </div>
    </form>
  );
};

export default ContactForm;
