import { useTranslation } from "next-i18next";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import Cookies from "universal-cookie";

const PrivacyPolicyBanner = () => {
  const { t } = useTranslation("common");
  const router = useRouter();

  const privacyPolicyCookieName = "privacy-policy-accepted";
  const privacyPolicyCookieValue = `accepted-${process.env.VERSION}`;

  const [privacyPolicyAccepted, setPrivacyPolicy] = useState("loading");

  useEffect(() => {
    const cookies = new Cookies();
    const cookie = cookies.get(privacyPolicyCookieName);
    setPrivacyPolicy(cookie);
  }, [privacyPolicyAccepted, privacyPolicyCookieValue, router.locale]);

  const acceptPrivacyPolicy = () => {
    const cookies = new Cookies();
    cookies.set(privacyPolicyCookieName, privacyPolicyCookieValue, { path: "/" });
    const value = cookies.get(privacyPolicyCookieName);
    setPrivacyPolicy(value);
  };

  // User has accepted the privacy policy, no need to display the banner
  if (privacyPolicyAccepted === privacyPolicyCookieValue || privacyPolicyAccepted === "loading") {
    return null;
  }

  const currentPath = router.pathname;
  const privacyPolicyPath = currentPath === "/privacy-policy";

  return (
    <div className="pointer-events-none fixed bottom-0 left-0 right-0 top-0 z-20 flex h-full w-screen items-end justify-center">
      <div className="pointer-events-auto absolute rounded-t border-t border-light-border bg-light px-4 py-6 text-light-text shadow-lg sm:mb-8 sm:max-w-[600px] sm:rounded sm:border">
        <p>
          {t("privacy-policy.banner.content")}{" "}
          <Link className="link-default" href={"/privacy-policy"} title={`${t("navigation.home")}`}>
            {t("privacy-policy.banner.link")}
          </Link>
          {"."}
        </p>
        <button
          className="mt-5 h-[46px] w-full rounded-sm bg-light-accent p-3 text-lg font-semibold leading-none text-light transition-colors hover:bg-light-accent-hover"
          onClick={acceptPrivacyPolicy}
        >
          {t("privacy-policy.banner.accept")}
        </button>
      </div>
    </div>
  );
};

export default PrivacyPolicyBanner;
