import { useEffect, useState } from "react";

export const useLocalStorage = <T>(key: string, initialValue: T) => {
  const isClient = typeof window !== "undefined";
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (isClient) {
      try {
        const item = window.localStorage.getItem(key);
        return item ? (JSON.parse(item) as T) : initialValue;
      } catch (error) {
        console.log(error);
      }
    }
    return initialValue;
  });

  useEffect(() => {
    if (isClient) {
      try {
        window.localStorage.setItem(key, JSON.stringify(storedValue));
      } catch (error) {
        console.log(error);
      }
    }
  }, [isClient, key, storedValue]);

  return [storedValue, setStoredValue] as const;
};
