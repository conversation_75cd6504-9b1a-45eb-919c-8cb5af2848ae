import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Head from "next/head";

import ContactForm from "@/components/ContactForm";
import { club } from "@/data/data";

export default function Contact() {
  const { t } = useTranslation("common");
  const headTitle = `${t("contact.title")} | ${club.businessName}`;

  return (
    <>
      <Head>
        <title>{headTitle}</title>
        <meta property="og:title" content={headTitle} />
        <meta property="og:description" content={headTitle} />
        <meta name="description" content={t("meta.description")} />
      </Head>

      <section className="relative flex min-h-[100vh] w-full flex-col py-10 md:min-w-[500px]">
        <div className="border-content mx-auto mb-10 w-full">
          <h3 className="mb-5 text-2xl font-semibold">{t("contact.info")}</h3>
          <p className="mt-2 font-semibold">{t("contact.chairman-label")}</p>
          <p className="mt-2"><PERSON><PERSON></p>
          <p className="mt-2 font-semibold">{t("contact.vice-chairman-label")}</p>
          <p className="mt-2">Panu Partanen</p>
          <p className="mt-2 font-semibold">{t("contact.secretary-label")}</p>
          <p className="mt-2">Elias Päivinen</p>
          <p className="mt-2 font-semibold">{t("contact.treasurer-label")}</p>
          <p className="mt-2">Ere Siili</p>
          <p className="mt-2 font-semibold">{t("contact.members-label")}</p>
          <p className="mt-2">Sami Pitkänen</p>
          <p className="mt-2">Sami Öhman</p>
        </div>

        <div className="border-content">
          <h3 className="text-2xl font-semibold">{t("contact.contact-us")}</h3>
          <ContactForm />
        </div>
      </section>
    </>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ["common"])),
    },
  };
}
