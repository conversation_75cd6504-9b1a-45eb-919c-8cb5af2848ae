# Arm Wrestling X

## Environments

Hosted on Vercel for free.

Updating the main branch will automatically trigger changes in the production (live) environment.

- Production: `https://www.armfightjkl.fi`

## Environment variables

```sh
CONTACT_EMAIL=<EMAIL>
CONTACT_PASSWORD=secret
ENV_NAME=development
SITE_URL=https://www.armfightjkl.fi
```

## Development

Node required.

```sh
npm install
npm run dev
```

## Relevant packages

### Icons

[Tabler icons](https://tabler-icons.io/) is very easy to use, has lots of variety and neutral style to fit any design.

### Prettier

Prettier has additional two packages to help with formatting. Those packages will sort imports (`@trivago/prettier-plugin-sort-imports`) and Tailwind classnames (`prettier-plugin-tailwindcss`) to a logical order.

## Translations

It is important to remember the following from [next-i18next](https://github.com/i18next/next-i18next#usetranslation) README.md

_Do NOT use the `useTranslation` export of `react-i18next`, but ONLY use the on of `next-i18next`!_

Otherwise serverside translations and clientside will be mismatched. This is very easy to miss with IDE auto-suggestions, and might lead to unnecessary debugging session.
