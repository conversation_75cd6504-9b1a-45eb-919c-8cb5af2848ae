@import "tailwindcss";
@config "../tailwind.config.js";
@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  font-family:
    "Arial",
    Poppins,
    Helvetica,
    sans-serif,
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    Fira Sans,
    Droid Sans;
}

@layer base {
  body {
    @apply bg-light text-light-text;
  }

  /* section {
    @apply px-4;
    @apply md:mx-auto;
    @apply md:px-6;
    @apply mx-auto;
    @apply max-w-5xl;
  } */

  a.link-default {
    @apply text-light-accent underline;
  }
  a.link-default:hover {
    @apply no-underline;
  }

  a.link-navigation {
    @apply underline-offset-4;
  }

  a.link-navigation:hover {
    @apply underline;
  }

  a.link-footer {
    @apply text-light underline hover:no-underline;
    @apply transition-colors;
  }

  a.link-footer:hover {
    @apply no-underline;
  }

  a.link-button {
    @apply bg-light-accent text-light rounded-sm p-3 no-underline;
  }

  a.link-button:hover {
    @apply bg-light-accent-hover;
  }

  input,
  textarea {
    @apply outline-light-text;
  }

  .border-content {
    @apply rounded-lg;
    @apply border;
    @apply border-gray-200;
    @apply p-6;
    @apply shadow-lg;
  }

  .env-ribbon {
    background-color: #ef2929;
    color: #fff;
    position: fixed;
    clip-path: inset(0 -100%);
    box-shadow: 0 0 0 999px #ef2929;
    inset: auto 0 0 auto;
    transform-origin: 0 100%;
    transform: translate(29.3%) rotate(-45deg);
    z-index: 9999;
  }

  .table-spacing {
    border-collapse: separate;
    border-spacing: 0 8px;
  }
  .table-spacing tr {
    padding-bottom: 8px;
  }
  .table-spacing th,
  .table-spacing td {
    padding-right: 16px; /* Adjust the padding as needed */
    display: block;
    width: 100%;
    text-align: left; /* Ensure left alignment */
  }
  .table-spacing th {
    text-align: left; /* Keep the text alignment for th */
    vertical-align: top; /* Align th elements to the top vertically */
  }
  @media (min-width: 768px) {
    .table-spacing {
      border-spacing: 0 4px;
    }
    .table-spacing th,
    .table-spacing td {
      display: table-cell;
      width: auto;
    }
    .table-spacing th {
      white-space: nowrap;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
