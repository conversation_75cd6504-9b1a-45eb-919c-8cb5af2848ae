import Head from "next/head";

import Footer from "./Footer";
import Navigation from "./Navigation";

const Layout = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <Navigation />
      <main className="flex min-h-screen flex-col px-4 md:mx-auto md:px-6">
        <div className="mx-auto max-w-5xl">{children}</div>
      </main>
      <Footer />
    </>
  );
};

export default Layout;
