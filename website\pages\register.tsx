import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Head from "next/head";

import epassiLogo from "../public/employee-benefit/epassi-logo.png";
import edenredLogo from "../public/employee-benefit/edenred-logo.png";
import smartumLogo from "../public/employee-benefit/smartum-logo.png";

import { club } from "@/data/data";
import Link from "next/link";
import Image from "next/image";

export default function Register() {
  const { t } = useTranslation("common");
  const headTitle = `${t("register.title")} | ${club.businessName}`;

  return (
    <>
      <Head>
        <title>{headTitle}</title>
        <meta property="og:title" content={headTitle} />
        <meta property="og:description" content={headTitle} />
        <meta name="description" content={t("meta.description")} />
      </Head>
      <section className="relative flex min-h-[100vh] w-full flex-col py-10 md:min-w-[500px]">
        {/* Membership */}
        <div className="border-content mb-6">
          <h3 className="mb-10 text-2xl font-semibold">{t("register.welcome")}</h3>
          <p className="mb-3">{t("register.membership-info")}</p>
          <p className="mb-3 italic">{t("register.membership-commitment")}</p>
          <table className="table-spacing mb-3 border-spacing-y-2">
            <tbody>
              <tr>
                <th>{t("register.membership-join-label")}:</th>
                <td>
                  <Link
                    href="https://seurat.suomisport.fi/invite/8c31a784-5ccb-4feb-a99f-cf3c3e9e5a54"
                    target="_blank"
                    className="link-default"
                  >
                    {t("register.membership-join-value")}
                  </Link>
                </td>
              </tr>
              <tr>
                <th>{t("register.membership-payment-label")}:</th>
                <td>{t("register.membership-payment-value")}</td>
              </tr>
            </tbody>
          </table>
          <p className="mb-3">{t("register.membership-join-info")}</p>

          <p className="mb-3">{t("register.membership-support")}</p>

          <h2 className="my-4 text-lg font-semibold">{t("register.membership-payment-title")}</h2>
          <p>{t("register.membership-payment-instruction")}</p>
          <table className="table-spacing mb-3 border-spacing-y-2">
            <tbody>
              <tr>
                <th>{t("register.membership-payment-account-label")}:</th>
                <td>{t("register.membership-payment-account-value")}</td>
              </tr>
              <tr>
                <th>{t("register.membership-payment-message-label")}:</th>
                <td className="italic">
                  &quot;{t("register.membership-payment-message-value")}&quot;
                </td>
              </tr>
            </tbody>
          </table>
          <div className="mt-10 flex flex-wrap items-center justify-around gap-10">
            <Image loading="lazy" title="Epassi" alt="Epassi" src={epassiLogo} height={50} />

            <Image loading="lazy" title="Edenred" alt="Edenred" src={edenredLogo} height={50} />

            <Image loading="lazy" title="Smartum" alt="Smartum" src={smartumLogo} height={50} />
          </div>
        </div>

        {/* License */}
        <div className="border-content mb-10">
          <h3 className="mb-6 text-2xl font-semibold">{t("register.license-header")}</h3>

          <p className="mb-3">{t("register.license-info")}</p>

          <h2 className="my-4 text-lg font-semibold">{t("register.license-type-header")}</h2>

          <table className="table-spacing mb-3 border-spacing-y-2">
            <tbody>
              <tr>
                <th>{t("register.license-type-pro-label")}:</th>
                <td>{t("register.license-type-pro-value")}</td>
              </tr>
              <tr>
                <th>{t("register.license-type-novice-label")}:</th>
                <td>{t("register.license-type-novice-value")}</td>
              </tr>
              <tr>
                <th>{t("register.license-type-upgrade-label")}:</th>
                <td>{t("register.license-type-upgrade-value")}</td>
              </tr>
              <tr>
                <th>{t("register.license-type-support-label")}:</th>
                <td>{t("register.license-type-support-value")}</td>
              </tr>
            </tbody>
          </table>

          <p className="mb-3">{t("register.license-union")}</p>

          <table className="table-spacing mb-3 border-spacing-y-2">
            <tbody>
              <tr>
                <th>{t("register.license-buy-info")}:</th>
                <td>
                  <Link
                    className="link-default"
                    target="_blank"
                    href={"https://info.suomisport.fi"}
                    title={`${t("register.license-buy-link")}`}
                  >
                    {t("register.license-buy-link")}
                  </Link>
                </td>
              </tr>
              <tr>
                <th>{t("register.license-union-link-label")}:</th>
                <td>
                  <Link
                    target="_blank"
                    className="link-default"
                    href={"https://www.skvl.net"}
                    title={`${t("register.license-union-link-value")}`}
                  >
                    {t("register.license-union-link-value")}
                  </Link>
                </td>
              </tr>
            </tbody>
          </table>

          <p className="italic">{t("register.license-commitment")}</p>
        </div>

        {/* Insurance */}
        <div className="border-content mb-10">
          <h3 className="mb-6 text-2xl font-semibold">{t("register.insurance-header")}</h3>
          <p className="mb-3">{t("register.insurance-info")}</p>
          <table className="table-spacing mb-3 border-spacing-y-2">
            <tbody>
              <tr>
                <th>{t("register.insurance-buy-info")}:</th>
                <td>
                  <Link
                    target="_blank"
                    className="link-default"
                    href={"https://info.suomisport.fi"}
                    title={`${t("register.insurance-buy-link")}`}
                  >
                    {t("register.insurance-buy-link")}
                  </Link>
                </td>
              </tr>
            </tbody>
          </table>

          <p className="mb-3 italic">
            <span>{t("register.insurance-underage")}</span>
          </p>
          <p className="italic">
            <span>{t("register.insurance-warning")}</span>
          </p>
        </div>

        {/* Education */}
        <div className="border-content mb-10">
          <h3 className="mb-6 text-2xl font-semibold">{t("register.education-header")}</h3>

          <p className="mb-3">{t("register.education-info")}</p>

          <table className="table-spacing mb-3 border-spacing-y-2">
            <tbody>
              <tr>
                <th>{t("register.education-clean-label")}:</th>
                <td>
                  <Link
                    target="_blank"
                    className="link-default"
                    href={"https://puhtaastiparas.fi"}
                    title={`${t("register.education-clean-link")}`}
                  >
                    {t("register.education-clean-link")}
                  </Link>{" "}
                  - <span>{t("register.education-clean-value")}</span>.
                </td>
              </tr>
              <tr>
                <th>{t("register.education-fair-label")}:</th>
                <td>
                  <Link
                    target="_blank"
                    className="link-default"
                    href={"https://puhtaastiparas.fi"}
                    title={`${t("register.education-fair-link")}`}
                  >
                    {t("register.education-fair-link")}
                  </Link>{" "}
                  - <span>{t("register.education-fair-value")}</span>
                </td>
              </tr>
              <tr>
                <th>{t("register.education-suek-label")}:</th>
                <td>
                  <Link
                    target="_blank"
                    className="link-default"
                    href={"https://suek.fi"}
                    title={`${t("register.education-fair-link")}`}
                  >
                    {t("register.education-suek-link")}
                  </Link>{" "}
                  - <span>{t("register.education-suek-value")}</span>
                </td>
              </tr>
            </tbody>
          </table>

          <p className="italic">{t("register.education-policy")}</p>
        </div>

        {/* Resignation */}
        <div className="border-content mb-10">
          <h3 className="mb-5 text-2xl font-semibold">{t("register.resignation-header")}</h3>
          <p className="mt-2">
            <span>{t("register.resignation-info")}</span>
          </p>
        </div>
      </section>
    </>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return { props: { ...(await serverSideTranslations(locale, ["common"])) } };
}
