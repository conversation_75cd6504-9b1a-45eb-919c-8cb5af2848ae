import { useTranslation } from "next-i18next";

/** In case something goes wrong... */
const ErrorBanner = ({ onDismiss }: { onDismiss: () => void }) => {
  const { t } = useTranslation("common");

  return (
    <div className="fixed bottom-0 left-0 right-0 top-0 z-20 flex h-screen w-screen items-end justify-center">
      <div className="absolute w-full rounded-t border-t border-light-error bg-light px-4 py-6 text-light-error shadow-lg sm:mb-8 sm:max-w-[600px] sm:rounded sm:border">
        <p className="font-semibold">{t("contact.error-message")}</p>
        <button
          className="text-dark-text mt-5 h-[46px] w-full rounded-sm bg-black p-3 text-lg leading-none transition-colors hover:bg-neutral-800"
          onClick={onDismiss}
        >
          {"Ok"}
        </button>
      </div>
    </div>
  );
};

export default ErrorBanner;
