import { contactEmail, transporter } from "@/utilities/transporter";
import type { NextApiRequest, NextApiResponse } from "next";

import type { ContactFormData } from "@/interfaces/contactForm";
import {
  contactRequestTemplate,
  contactResponseEn,
  contactResponseFi,
} from "@/email-templates/contact";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ health: boolean; message: string; error?: string }>,
) {
  if (req.method === "POST") {
    const { email, message } = req.body as ContactFormData;
    const { language } = req.query;

    if (!email || !message) {
      return res.status(400).json({ health: true, message: "Bad request." });
    }

    try {
      await transporter.sendMail({
        from: contactEmail,
        to: contactEmail,
        ...contactRequestTemplate({ email, message }),
      });

      const responseTemplate = language === "en" ? contactResponseEn() : contactResponseFi();

      await transporter.sendMail({
        from: contactEmail,
        to: email,
        ...responseTemplate,
      });
    } catch (err) {
      const error = err as Error;
      return res
        .status(500)
        .json({ health: false, message: "Send contact form failed", error: error.message });
    }

    return res.status(200).json({
      health: true,
      message: "Message received.",
    });
  }

  return res.status(400).json({ health: true, message: "Bad request." });
}
