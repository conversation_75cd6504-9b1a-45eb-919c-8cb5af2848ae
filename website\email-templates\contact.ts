import { ContactFormData } from "@/interfaces/contactForm";
import { convertLineBreaksToHTML } from "@/utilities/email";

export const contactRequestTemplate = (data: ContactFormData) => ({
  subject: `Yhteydenottopyyntö - ${data.email}`,
  html: `<p>Sähköposti: ${data.email}</p>
  <p>Viesti:<br>${convertLineBreaksToHTML(data.message)}</p>`,
});

export const contactResponseFi = () => ({
  subject: `Ki<PERSON>s yhteydenotostasi`,
  html: `<p>Palaamme asiaan mahdollisimman nopeasti.</p>
  <p>Ystävällisin terveisin,<br>Arm Fight JKL ry<br>3443518-4<br>armfightjkl.fi</p>`,
});

export const contactResponseEn = () => ({
  subject: `Thank you for contacting`,
  html: `<p>We will return to the matter as soon as possible.</p>
  <p>Best regards,<br>Arm Fight JKL ry<br>3443518-4<br>armfightjkl.fi</p>`,
});
