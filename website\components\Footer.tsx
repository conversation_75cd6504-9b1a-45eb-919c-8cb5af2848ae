import { useTranslation } from "next-i18next";
import Link from "next/link";
import {
  IconBrandInstagram,
  IconBrandFacebook,
  IconBrandYoutube,
  IconBrandWhatsapp,
} from "@tabler/icons-react";
import Email from "./common/Email";
import { club } from "@/data/data";

const Footer = () => {
  const { t } = useTranslation("common");

  return (
    <footer className="bg-light-accent px-4 py-10 text-gray-50 md:pt-14">
      <div className="mx-auto mb-10 flex max-w-5xl flex-col justify-between md:mb-14 md:flex-row">
        <div className="mb-8 flex flex-col text-center md:mb-0 md:text-left">
          <p className="mb-3 text-xl font-semibold">{club.businessName}</p>
          <Email email={club.email} classNames="link-footer" />
          <p className="mb-3">{club.businessId}</p>
          <p>{t("footer.location-city")}</p>
          <p>{t("footer.location-region")}</p>
        </div>

        {/* Main pages */}
        <ul className="mb-10 flex flex-col gap-1 text-center md:mb-0 md:text-left">
          <li>
            <Link className="link-footer" href={"/"} title={`${t("navigation.home")}`}>
              {t("navigation.home")}
            </Link>
          </li>
          <li>
            <Link className="link-footer" href={"/register"} title={`${t("navigation.register")}`}>
              {t("navigation.register")}
            </Link>
          </li>
          <li>
            <Link className="link-footer" href={"/contact"} title={`${t("navigation.contact")}`}>
              {t("navigation.contact")}
            </Link>
          </li>
        </ul>

        <div className="flex flex-col">
          <div className="mb-3">
            <p className="mb-3 text-center">{t("footer.join-group")}</p>
            <ul className="flex justify-center gap-4">
              <li>
                <Link
                  target="_blank"
                  className="flex h-10 w-10 items-center justify-center rounded-full bg-white transition-colors hover:bg-slate-300"
                  href={"https://chat.whatsapp.com/LdHMwYlZZM154Y7mDmkt9d"}
                  title={"whatsapp"}
                >
                  <IconBrandWhatsapp size={32} color="#25d366" />
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <p className="mb-3 text-center">{t("footer.follow-us")}</p>
            <ul className="flex justify-center gap-4">
              <li>
                <Link
                  target="_blank"
                  className="flex h-10 w-10 items-center justify-center rounded-full bg-white transition-colors hover:bg-slate-300"
                  href={"https://www.instagram.com/armfightjkl"}
                  title={"instagram"}
                >
                  <IconBrandInstagram size={32} color="#dd2a7b" />
                </Link>
              </li>
              <li>
                <Link
                  target="_blank"
                  className="flex h-10 w-10 items-center justify-center rounded-full bg-white transition-colors hover:bg-slate-300"
                  href={"https://www.youtube.com/@armfightjkl"}
                  title={"youtube"}
                >
                  <IconBrandYoutube size={30} color="#c4302b" />
                </Link>
              </li>
              <li>
                <Link
                  target="_blank"
                  className="flex h-10 w-10 items-center justify-center rounded-full bg-white transition-colors hover:bg-slate-300"
                  href={"https://www.facebook.com/armfightjkl"}
                  title={"facebook"}
                >
                  <IconBrandFacebook size={30} color="#3b5998" />
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div className="mx-4 text-center text-sm">
        <p>
          &copy; {new Date().getFullYear()} {club.businessName}.{" "}
          <span className="whitespace-nowrap">
            {t("privacy-policy.our-policy")}{" "}
            <Link
              className="link-footer whitespace-nowrap"
              href={"/privacy-policy"}
              title={`${t("navigation.privacy-policy")}`}
            >
              {t("privacy-policy.banner.link")}
            </Link>
            {"."}
          </span>
        </p>
      </div>
    </footer>
  );
};

export default Footer;
