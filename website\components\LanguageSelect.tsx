import { useClickaway } from "@/hooks";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useRef, useState } from "react";

const languages: string[] = ["fi", "en"];

export const LanguageSelect = () => {
  const [isOpen, setIsOpen] = useState(false);

  const router = useRouter();
  const { t, i18n } = useTranslation("common");

  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleChangeLanguage = async (locale: string) => {
    await i18n.changeLanguage(locale);

    const newPath = `/${locale}${router.asPath}`;
    router.push(newPath, newPath, { locale });
    setIsOpen((prev) => !prev);
  };

  useClickaway(isOpen, dropdownRef as React.RefObject<HTMLDivElement>, () => setIsOpen(false));

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      <div>
        <button
          type="button"
          className="flex justify-center rounded-full bg-white text-light-accent shadow-sm ring-gray-300 transition-colors hover:bg-slate-300"
          id="menu-button"
          aria-expanded={isOpen}
          aria-haspopup="true"
          aria-label={t("navigation.language")}
          onClick={() => setIsOpen(!isOpen)}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={32}
            height={32}
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
            className="icon icon-tabler icons-tabler-outline icon-tabler-world rotate-6"
          >
            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
            <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0" />
            <path d="M3.6 9h16.8" />
            <path d="M3.6 15h16.8" />
            <path d="M11.5 3a17 17 0 0 0 0 18" />
            <path d="M12.5 3a17 17 0 0 1 0 18" />
          </svg>
        </button>
      </div>

      {isOpen && (
        <div
          className="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="menu-button"
          tabIndex={-1}
        >
          <div className="py-1" role="none">
            {languages.map((locale) => {
              const activeClasses = locale === router.locale ? " font-semibold" : "";
              return (
                <button
                  key={locale}
                  onClick={() => handleChangeLanguage(locale)}
                  className={`block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100${activeClasses} transition-colors`}
                  role="menuitem"
                  tabIndex={-1}
                >
                  {t(`language.${locale}`)}
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
