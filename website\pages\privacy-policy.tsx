import { club, dataProtectionOfficer } from "@/data/data";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Head from "next/head";

import Email from "../components/common/Email";

export default function PrivacyPolicy() {
  const { t } = useTranslation("common");

  const headTitle = `${t("privacy-policy.title")} | ${club.brand}`;

  return (
    <>
      <Head>
        <title>{headTitle}</title>
        <meta property="og:title" content={headTitle} />
        <meta property="og:description" content={headTitle} />
        <meta name="description" content={t("meta.description")} />
      </Head>

      <div className="mx-auto mt-10 max-w-3xl">
        <section className="mb-10">
          <h3 className="my-4 text-2xl font-semibold">{t("privacy-policy.title")}</h3>
          <p>{t("privacy-policy.summary")}</p>
        </section>

        <section className="mb-10">
          <p>
            <span className="font-semibold">
              {t("privacy-policy.information-we-collect-header")}:{" "}
            </span>
            {t("privacy-policy.information-we-collect-content")}
          </p>
        </section>

        <section className="mb-10">
          <p>
            <span className="font-semibold">{t("privacy-policy.use-of-information-header")}: </span>
            {t("privacy-policy.use-of-information-content")}
          </p>
        </section>

        <section className="mb-10">
          <p>
            <span className="font-semibold">{t("privacy-policy.data-security-header")}: </span>
            {t("privacy-policy.data-security-content")}
          </p>
        </section>

        <section className="mb-10">
          <p>
            <span className="font-semibold">{t("privacy-policy.cookies-header")}: </span>
            {t("privacy-policy.cookies-content")}
          </p>
        </section>

        <section className="mb-10">
          <p>
            <span className="font-semibold">
              {t("privacy-policy.links-to-third-party-websites-header")}:{" "}
            </span>
            {t("privacy-policy.links-to-third-party-websites-content")}
          </p>
        </section>

        <section className="mb-10">
          <p>
            <span className="font-semibold">{t("privacy-policy.data-retention-header")}: </span>
            {t("privacy-policy.data-retention-content")}
          </p>
        </section>

        <section className="mb-10">
          <p>
            <span className="font-semibold">
              {t("privacy-policy.data-protection-rights-header")}:{" "}
            </span>
            {t("privacy-policy.data-protection-rights-content")}
          </p>
        </section>

        <section className="mb-10">
          <p>
            <span className="font-semibold">
              {t("privacy-policy.changes-to-the-privacy-policy-header")}:{" "}
            </span>
            {t("privacy-policy.changes-to-the-privacy-policy-content")}
          </p>
        </section>

        <section className="mb-10">
          <p>{t("privacy-policy.contact")}</p>
        </section>

        <section className="mb-24">
          <h2 className="mb-4 text-lg font-semibold">
            {t("privacy-policy.data-protection-officer")}
          </h2>
          <p>
            <span className="font-semibold">{t("label.name")}</span>: {dataProtectionOfficer.name}
          </p>
          <p>
            <span className="font-semibold">{t("label.email")}</span>: <Email email={club.email} />
          </p>
        </section>
      </div>
    </>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ["common"])),
    },
  };
}
