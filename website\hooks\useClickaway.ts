import { useEffect } from "react";

/**
 * Close a component when clicking outside or on ESC key press.
 * @param {boolean} isOpen - State indicating whether the component is open.
 * @param {React.RefObject<HTMLElement>} ref - Ref attached to the component.
 * @param {Function} onClose - Function to call to close the component.
 */
export const useClickaway = (
  isOpen: boolean,
  ref: React.RefObject<HTMLElement>,
  onClose: () => void,
) => {
  useEffect(() => {
    const handleEscapeKeyPress = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    const handleFormClickAway = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!ref.current || (ref.current && !ref.current.contains(target))) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("click", handleFormClickAway);
      document.addEventListener("keydown", handleEscapeKeyPress);
    }

    return () => {
      document.removeEventListener("click", handleFormClickAway);
      document.removeEventListener("keydown", handleEscapeKeyPress);
    };
  }, [isOpen, ref, onClose]);
};
