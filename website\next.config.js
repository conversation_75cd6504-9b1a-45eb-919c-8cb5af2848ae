/** @type {import('next').NextConfig} */

const { i18n } = require("./next-i18next.config");

const nextConfig = {
  async headers() {
    return [
      {
        source: "/",
        headers: [
          // This header indicates whether the site should be allowed to be displayed within an iframe. This can prevent against clickjacking attacks.
          // This header has been superseded by CSP's frame-ancestors option, which has better support in modern browsers.
          // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          {
            key: "x-another-custom-header",
            value: "my other custom header value",
          },
          // This header controls DNS prefetching, allowing browsers to proactively perform domain name resolution on external links, images, CSS, JavaScript, and more. This prefetching is performed in the background, so the DNS is more likely to be resolved by the time the referenced items are needed. This reduces latency when the user clicks a link.'
          // https://nextjs.org/docs/pages/api-reference/next-config-js/headers#x-dns-prefetch-control
          {
            key: "X-DNS-Prefetch-Control",
            value: "on",
          },
        ],
      },
    ];
  },
  reactStrictMode: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
  i18n,
  env: {
    VERSION: process.env.npm_package_version,
    CONTACT_EMAIL: process.env.CONTACT_EMAIL || null,
    CONTACT_PASSWORD: process.env.CONTACT_PASSWORD || null,
    // Optional
    // The purpose is to distinguish the development environment
    ENV_NAME: process.env.ENV_NAME || "",
  },
};

module.exports = nextConfig;
