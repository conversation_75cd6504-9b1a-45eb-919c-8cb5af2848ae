import { Analytics } from "@vercel/analytics/react";
import { appWithTranslation } from "next-i18next";
import type { AppProps } from "next/app";
import { <PERSON>o, Poppins } from "next/font/google";

import Layout from "@/components/Layout";

import PrivacyPolicyBanner from "../components/PrivacyPolicyBanner";
import "../styles/globals.css";
import { production } from "../utilities/environment";

const poppins = Poppins({
  subsets: ["latin-ext"],
  weight: ["400", "700"],
});

function App({ Component, pageProps }: AppProps) {
  return (
    <div className={poppins.className}>
      <Analytics />
      {!production && <div className="env-ribbon">{process.env.ENV_NAME}</div>}
      <PrivacyPolicyBanner />
      <Layout>
        <Component {...pageProps} />
      </Layout>
    </div>
  );
}

export default appWithTranslation(App);
