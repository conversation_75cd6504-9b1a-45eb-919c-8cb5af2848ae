import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Head from "next/head";
import brandLogo from "../public/arm-fight-jkl/brand-logo.png";
import chairmansFunImage from "../public/community/chairmans-fun.jpg";
import sponsorGnonceOyLogo from "../public/sponsor/gnonce-oy-logo.png";
import sponsorPartakoneOyLogo from "../public/sponsor/partakone-oy-logo.png";
import kisakauppaLogo from "../public/shop/kisakauppa-logo.png";
import brandTShirt from "../public/shop/brand-t-shirt.png";
import brandHoodie from "../public/shop/brand-hoodie.png";
import Image from "next/image";
import Link from "next/link";
import { club } from "@/data/data";
import { useIsMobile } from "@/hooks";
import { IconInfoCircle, IconMessage } from "@tabler/icons-react";
import { IconCancel } from "@tabler/icons-react";

const isDateInPast = (date: string) => {
  const [day, month, year] = date.split(".").map(Number);
  const inputDate = new Date(year, month - 1, day);
  inputDate.setHours(0, 0, 0, 0);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return inputDate < today;
};

export default function Home() {
  const { t } = useTranslation("common");

  const isMobile = useIsMobile();

  const headTitle = `${t("home.title")} | ${club.businessName}`;

  // Format dd.mm.yyyy
  const canceledTrainingDates: string[] = ["21.04.2025"];
  // Format dd.mm.yyyy
  const trainingExceptionDates: { date: string; time: string }[] = [
    { date: "26.04.2025", time: "18:00 - 20:00" },
  ];

  const allCanceledTrainingDatesInPast = canceledTrainingDates.every(isDateInPast);
  const allTrainingExceptionDatesInPast = trainingExceptionDates.every((exception) =>
    isDateInPast(exception.date),
  );

  return (
    <>
      <Head>
        <title>{headTitle}</title>
        <meta property="og:title" content={headTitle} />
        <meta property="og:description" content={headTitle} />
        <meta name="description" content={t("meta.description")} />
      </Head>

      <section className="relative flex min-h-[100vh] w-full flex-col py-10 md:min-w-[500px]">
        <div className="mb-10 rounded-lg border border-gray-200 shadow-lg">
          <div className="relative flex max-h-[550px] min-h-[550px] w-full">
            <Image
              loading="lazy"
              fill
              style={{ objectFit: "cover" }}
              // height={isMobile ? 300 : 400}
              alt="Arm Fight JKL"
              title="Arm Fight JKL"
              src={chairmansFunImage}
            />
          </div>

          <div className="m-6">
            <p className="text-center text-xl font-medium">{t("home.welcome")}</p>
          </div>
          <div className="m-6">
            <p className="text-center text-xl font-medium">{t("home.description")}</p>
          </div>
        </div>

        {/* Training */}
        <div className="border-content mb-10">
          <h3 className="mb-6 text-2xl font-semibold">{t("home.training-header")}</h3>

          <p className="mb-3">{t("home.training-info")}</p>

          <table className="table-spacing mb-3 border-spacing-y-2">
            <tbody>
              <tr>
                <th>{t("home.training-place-label")}:</th>
                <td>
                  <Link
                    target="_blank"
                    className="link-default"
                    href={
                      "https://www.jyvaskyla.fi/liikunta/liikuntapaikat/sisaliikuntapaikat/monitoimitalo"
                    }
                    title={`${t("home.training-place-name")}`}
                  >
                    {t("home.training-place-name")}
                  </Link>
                  ,{" "}
                  <Link
                    target="_blank"
                    className="link-default"
                    href={"https://maps.app.goo.gl/GveZjDqpbtApW6aQ7"}
                    title={`${t("home.training-place-address")}`}
                  >
                    {t("home.training-place-address")}
                  </Link>
                </td>
              </tr>
              <tr>
                <th>{t("home.training-time-label")}:</th>
                <td>{t("home.training-time-value")}</td>
              </tr>
              <tr>
                <th>{t("home.training-parking-label")}:</th>
                <td>
                  <Link
                    target="_blank"
                    className="link-default"
                    href={
                      "https://www.jyvaskyla.fi/sites/default/files/2025-04/hippoksen_alueen_pysakoinnin_ohjeistus.pdf"
                    }
                    title={`${t("home.traning-parking-instruction")}`}
                  >
                    {t("home.traning-parking-instruction")}
                  </Link>
                </td>
              </tr>

              {trainingExceptionDates.length > 0 && !allTrainingExceptionDatesInPast && (
                <tr>
                  <th>
                    <div className="flex flex-row items-center">
                      <IconInfoCircle
                        size={45}
                        className="mr-3 basis-1/12 md:basis-2/12"
                        color="#0074AB"
                      />
                      <p className="basis-11/12 md:basis-10/12">{t("home.training-exceptions")}</p>
                    </div>
                  </th>
                  <td>
                    {trainingExceptionDates.map((exception) => {
                      return (
                        !isDateInPast(exception.date) && (
                          <p key={exception.date}>
                            {exception.date} {exception.time}
                          </p>
                        )
                      );
                    })}
                  </td>
                </tr>
              )}
              {canceledTrainingDates.length > 0 && !allCanceledTrainingDatesInPast && (
                <tr>
                  <th>
                    <div className="flex flex-row items-center">
                      <IconCancel
                        size={45}
                        className="mr-3 basis-1/12 md:basis-2/12"
                        color="#FF0000"
                      />
                      <p className="basis-11/12 md:basis-10/12">{t("home.training-cancel")}</p>
                    </div>
                  </th>
                  <td>
                    {canceledTrainingDates.map((canceled) => {
                      return (
                        !isDateInPast(canceled) && (
                          <p key={canceled}>
                            {canceled} {canceled}
                          </p>
                        )
                      );
                    })}
                  </td>
                </tr>
              )}
            </tbody>
          </table>

          <p className="mb-3">{t("home.training-place-info")}</p>

          <p className="mb-3">{t("home.training-cheer")}</p>

          <p className="italic">{t("home.training-guardian-info")}</p>
        </div>

        {/* Membership */}
        <div className="mb-10 rounded-lg border border-gray-200 shadow-lg">
          <div className="m-6">
            <h3 className="mb-6 text-2xl font-semibold">{t("home.membership-header")}</h3>

            <p className="mb-3">{t("home.membership-info")}</p>

            <div className="mt-10 flex flex-wrap items-center justify-center">
              <Link
                className="link-button"
                href={"/register"}
                title={`${t("home.membership-registration-link")}`}
              >
                {t("home.membership-join")}
              </Link>
            </div>
          </div>
          <div className="flex items-center justify-center rounded-b-lg bg-black">
            <Image
              loading="lazy"
              width={isMobile ? 250 : 300}
              height={isMobile ? 250 : 300}
              alt="Arm Fight JKL"
              title="Arm Fight JKL"
              src={brandLogo}
            />
          </div>
        </div>

        {/* FAQ */}
        <div className="border-content mb-10">
          <h3 className="mb-6 text-2xl font-semibold">{t("home.faq-header")}</h3>

          <div className="mb-5">
            <p className="mb-3 text-lg italic">&quot;{t("home.faq-question-1")}&quot;</p>
            <div className="flex flex-row items-center">
              <IconMessage size={30} className="mr-3 basis-1/12" color="#0074AB" />
              <p className="basis-11/12">{t("home.faq-answer-1")}</p>
            </div>
          </div>

          <div className="mb-5">
            <p className="mb-3 text-lg italic">&quot;{t("home.faq-question-2")}&quot;</p>
            <div className="flex flex-row items-center">
              <IconMessage size={30} className="mr-3 basis-1/12" color="#0074AB" />
              <p className="basis-11/12">{t("home.faq-answer-2")}</p>
            </div>
          </div>

          <div className="mb-5">
            <p className="mb-3 text-lg italic">&quot;{t("home.faq-question-3")}&quot;</p>
            <div className="flex flex-row items-center">
              <IconMessage size={30} className="mr-3 basis-1/12" color="#0074AB" />
              <p className="basis-11/12">{t("home.faq-answer-3")}</p>
            </div>
          </div>

          <div className="mb-5">
            <p className="mb-3 text-lg italic">&quot;{t("home.faq-question-4")}&quot;</p>
            <div className="flex flex-row items-center">
              <IconMessage size={30} className="mr-3 basis-1/12" color="#0074AB" />
              <p className="basis-11/12">{t("home.faq-answer-4")}</p>
            </div>
          </div>

          <div className="mb-5">
            <p className="mb-3 text-lg italic">&quot;{t("home.faq-question-5")}&quot;</p>
            <div className="flex flex-row items-center">
              <IconMessage size={30} className="mr-3 basis-1/12" color="#0074AB" />
              <p className="basis-11/12">{t("home.faq-answer-5")}</p>
            </div>
          </div>
        </div>

        {/* Shop */}
        <div className="border-content mb-10">
          <h3 className="mb-6 text-2xl font-semibold">{t("home.shop-header")}</h3>

          <p>{t("home.shop-content")}</p>
          <table className="table-spacing mb-3 border-spacing-y-2">
            <tbody>
              <tr>
                <th>{t("home.shop-label")}:</th>

                <td>
                  <Link
                    target="_blank"
                    className="link-default"
                    href={"https://kisakauppa.fi/kauppa/armfightjkl"}
                    title={`${t("home.shop-image-link")}`}
                  >
                    Kisakauppa.fi
                  </Link>
                </td>
              </tr>
            </tbody>
          </table>
          <div className="my-10 flex flex-wrap items-center justify-around">
            <Image
              loading="lazy"
              title={t("home.shop-brand-t-shirt")}
              alt={t("home.shop-brand-t-shirt")}
              src={brandTShirt}
              height={400}
            />
            <Image
              loading="lazy"
              title={t("home.shop-brand-hoodie")}
              alt={t("home.shop-brand-hoodie")}
              src={brandHoodie}
              height={400}
            />
          </div>

          <div className="mt-10 flex items-center justify-center">
            <Link
              target="_blank"
              className="link-default inline-block rounded-lg p-3 transition-colors hover:bg-slate-300"
              href={"https://kisakauppa.fi/kauppa/armfightjkl"}
              title={`${t("home.shop-image-link")}`}
            >
              <Image
                loading="lazy"
                title="Kisakauppa.fi"
                alt="Kisakauppa.fi"
                src={kisakauppaLogo}
                height={125}
              />
            </Link>
          </div>
        </div>

        {/* Partners */}
        <div className="border-content mb-10">
          <h3 className="mb-6 text-2xl font-semibold">{t("home.partners-header")}</h3>

          <p>{t("home.partners-info")}</p>
          <div className="mt-10 flex flex-wrap items-center justify-center gap-10">
            <Link
              target="_blank"
              className="link-default inline-block"
              href={"https://gnonce.com"}
              title={`Gnonce Oy - 2879826-1`}
            >
              <Image
                loading="lazy"
                className="rounded"
                title="Gnonce Oy - 2879826-1"
                alt="Gnonce Oy - 2879826-1"
                src={sponsorGnonceOyLogo}
                height={80}
              />
            </Link>
            <Image
              loading="lazy"
              className="rounded"
              title="Partakone Oy - 3251012-8"
              alt="Partakone Oy - 3251012-8"
              src={sponsorPartakoneOyLogo}
              height={80}
            />
          </div>
        </div>
      </section>
    </>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return { props: { ...(await serverSideTranslations(locale, ["common"])) } };
}
