import { club } from "@/data/data";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Head from "next/head";

export default function Custom500() {
  const { t } = useTranslation("common");
  const headTitle = `${t("500.title")} | ${club.businessName}`;

  return (
    <>
      <Head>
        <title>{headTitle}</title>
        <meta property="og:title" content={headTitle} />
        <meta property="og:description" content={headTitle} />
        <meta name="description" content={t("meta.description")} />
      </Head>

      <div className="flex flex-grow flex-col items-center justify-center">
        <div>{t("500.title")}</div>
      </div>
    </>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ["common"])),
    },
  };
}
